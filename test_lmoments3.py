#!/usr/bin/env python3
"""
Test script for lmoments3 library with provided tr and tr3 values.
Calculates mean values and distribution parameters for various distributions.
"""

import numpy as np
from lmoments3 import distr
import pandas as pd

def check_available_distributions():
    """Check what distributions are available in lmoments3"""
    print("Available distributions in lmoments3.distr:")
    for attr in dir(distr):
        if not attr.startswith('_'):
            print(f"  {attr}")
    print()

def test_lmoments3_distributions():
    """
    Test lmoments3 library with provided tr and tr3 values.
    Calculate parameters for each distribution using l1=1, tr, and tr3.
    """
    
    # Your provided data
    tr_values = [.1209, .0915, .1124, .1032, .0967, .1328, .1008, .1143, .1107, .1179, 
                 .1308, .1119, .1018, .1025, .1054, .1174, .1115, .1003, .1046]
    
    tr3_values = [.0488, .0105, .0614, .0417, -.0134, -.0176, .0943, .0555, .0478, .0492, 
                  .0940, -.0429, .0435, .0182, -.0224, .0124, -.0346, .0446, .0128]
    
    print("Original tr values:")
    print(tr_values)
    print(f"\nMean tr: {np.mean(tr_values):.6f}")
    print(f"Std tr:  {np.std(tr_values):.6f}")
    
    print("\nOriginal tr3 values:")
    print(tr3_values)
    print(f"\nMean tr3: {np.mean(tr3_values):.6f}")
    print(f"Std tr3:  {np.std(tr3_values):.6f}")
    
    # Calculate mean values
    mean_tr = np.mean(tr_values)
    mean_tr3 = np.mean(tr3_values)
    
    print(f"\n{'='*60}")
    print("DISTRIBUTION PARAMETER ESTIMATION")
    print(f"{'='*60}")
    print(f"Using: l1 = 1.0, tr = {mean_tr:.6f}, tr3 = {mean_tr3:.6f}")
    
    # L-moment ratios for fitting (l1, l2/l1, l3/l2, l4/l2)
    # Since l1=1 and tr=l2/l1, we have l2=tr
    # Since tr3=l3/l2, we have l3=tr3*l2=tr3*tr
    # We'll set t4=0 for simplicity (can be adjusted)
    l1 = 1.0
    l2 = mean_tr * l1  # l2 = tr * l1
    l3 = mean_tr3 * l2  # l3 = tr3 * l2
    t4 = 0.0  # Fourth L-moment ratio (set to 0 for now)
    
    lmom_ratios = [l1, l2, mean_tr3, t4]
    
    print(f"\nL-moment ratios used for fitting:")
    print(f"l1 = {l1:.6f}")
    print(f"l2 = {l2:.6f}")
    print(f"tr (l2/l1) = {mean_tr:.6f}")
    print(f"tr3 (l3/l2) = {mean_tr3:.6f}")
    print(f"t4 = {t4:.6f}")
    
    # Available distributions in lmoments3
    distributions = {
        'KAPPA': distr.kap,
        'GEV': distr.gev,
        'GLO': distr.glo,
        'GPA': distr.gpa,
        'PE3': distr.pe3,
        'GNO': distr.gno,  # Generalized Normal
        'GAM': distr.gam,  # Gamma
        'WAK': distr.wak,  # Wakeby
        'GUM': distr.gum   # Gumbel
    }
    
    results = {}
    
    print(f"\n{'='*60}")
    print("FITTING RESULTS")
    print(f"{'='*60}")
    
    for dist_name, dist_obj in distributions.items():
        try:
            print(f"\n{dist_name} Distribution:")
            print("-" * 30)
            
            # Fit distribution parameters using L-moments
            params = dist_obj.lmom_fit(lmom_ratios=lmom_ratios)
            
            print(f"Parameters: {params}")
            
            # Calculate theoretical L-moment ratios from fitted parameters
            theoretical_lmoms = dist_obj.lmom_ratios(**params)
            
            print(f"Theoretical L-moments:")
            print(f"  l1 = {theoretical_lmoms[0]:.6f}")
            print(f"  tr = {theoretical_lmoms[1]:.6f}")
            print(f"  tr3 = {theoretical_lmoms[2]:.6f}")
            print(f"  t4 = {theoretical_lmoms[3]:.6f}")
            
            # Calculate goodness of fit
            tr_diff = abs(theoretical_lmoms[1] - mean_tr)
            tr3_diff = abs(theoretical_lmoms[2] - mean_tr3)
            
            print(f"Goodness of fit:")
            print(f"  |tr_fitted - tr_target| = {tr_diff:.6f}")
            print(f"  |tr3_fitted - tr3_target| = {tr3_diff:.6f}")
            
            results[dist_name] = {
                'parameters': params,
                'theoretical_lmoms': theoretical_lmoms,
                'tr_error': tr_diff,
                'tr3_error': tr3_diff
            }
            
        except Exception as e:
            print(f"Error fitting {dist_name}: {str(e)}")
            results[dist_name] = {'error': str(e)}
    
    # Find best fitting distribution
    print(f"\n{'='*60}")
    print("BEST FIT ANALYSIS")
    print(f"{'='*60}")
    
    valid_results = {k: v for k, v in results.items() if 'error' not in v}
    
    if valid_results:
        # Calculate combined error (you can weight these differently if needed)
        for dist_name, result in valid_results.items():
            result['combined_error'] = result['tr_error'] + result['tr3_error']
        
        # Sort by combined error
        sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['combined_error'])
        
        print("Distributions ranked by goodness of fit (lower error = better):")
        for i, (dist_name, result) in enumerate(sorted_results, 1):
            print(f"{i}. {dist_name}: Combined error = {result['combined_error']:.6f}")
        
        best_dist = sorted_results[0]
        print(f"\nBest fitting distribution: {best_dist[0]}")
        print(f"Parameters: {best_dist[1]['parameters']}")
    
    # Test with individual tr and tr3 pairs
    print(f"\n{'='*60}")
    print("INDIVIDUAL PAIR ANALYSIS")
    print(f"{'='*60}")
    
    print("Testing first 5 individual tr, tr3 pairs with KAPPA distribution:")
    for i in range(min(5, len(tr_values))):
        tr_i = tr_values[i]
        tr3_i = tr3_values[i]
        
        print(f"\nPair {i+1}: tr={tr_i:.4f}, tr3={tr3_i:.4f}")
        
        # Create L-moment ratios for this pair
        l1_i = 1.0
        l2_i = tr_i * l1_i
        lmom_ratios_i = [l1_i, l2_i, tr3_i, 0.0]
        
        try:
            params_i = distr.kap.lmom_fit(lmom_ratios=lmom_ratios_i)
            print(f"  KAPPA parameters: {params_i}")
            
            # Verify the fit
            theoretical_i = distr.kap.lmom_ratios(**params_i)
            print(f"  Verification - tr: {theoretical_i[1]:.4f}, tr3: {theoretical_i[2]:.4f}")
            
        except Exception as e:
            print(f"  Error: {str(e)}")

    # Summary statistics
    print(f"\n{'='*60}")
    print("SUMMARY STATISTICS")
    print(f"{'='*60}")

    tr_array = np.array(tr_values)
    tr3_array = np.array(tr3_values)

    print(f"TR (L-CV) Statistics:")
    print(f"  Count: {len(tr_values)}")
    print(f"  Mean: {np.mean(tr_array):.6f}")
    print(f"  Median: {np.median(tr_array):.6f}")
    print(f"  Std Dev: {np.std(tr_array):.6f}")
    print(f"  Min: {np.min(tr_array):.6f}")
    print(f"  Max: {np.max(tr_array):.6f}")
    print(f"  Range: {np.max(tr_array) - np.min(tr_array):.6f}")

    print(f"\nTR3 (L-Skewness) Statistics:")
    print(f"  Count: {len(tr3_values)}")
    print(f"  Mean: {np.mean(tr3_array):.6f}")
    print(f"  Median: {np.median(tr3_array):.6f}")
    print(f"  Std Dev: {np.std(tr3_array):.6f}")
    print(f"  Min: {np.min(tr3_array):.6f}")
    print(f"  Max: {np.max(tr3_array):.6f}")
    print(f"  Range: {np.max(tr3_array) - np.min(tr3_array):.6f}")

    print(f"\nCorrelation between TR and TR3: {np.corrcoef(tr_array, tr3_array)[0,1]:.6f}")

if __name__ == "__main__":
    check_available_distributions()
    test_lmoments3_distributions()
